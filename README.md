# 🪶 Quill - Your AI Writing Assistant

Quill is a floating AI writing assistant that enhances your writing experience across any application on your computer. It runs locally using Ollama, providing real-time writing assistance while maintaining your privacy.

## ✨ Features

- **Auto-Complete**: Smart sentence completion based on context
- **Auto-Write**: Generate complete texts from prompts
- **Rephrase**: Instantly rephrase selected text
- **Always Available**: Floating window that's there when you need it

## 🚀 Getting Started

1. Install dependencies:
```bash
pip install PyQt5 pyperclip langchain pynput
```

2. Install [Ollama](https://ollama.ai/)

3. Run the assistant:
```bash
python app.py
```

## 💡 Usage

- **Auto-Complete**: Type naturally, suggestions appear automatically
- **Auto-Write**: Click "Auto Write" and describe what you want to write
- **Rephrase**: Select text and use the rephrase tool to modify it
- **Move**: Drag the window anywhere on your screen

## 🔧 Requirements

- Python 3.8+
- Ollama
- Internet connection (for initial model download)

## 🔒 Privacy

All processing happens locally on your machine using Ollama.

---
Made with ❤️ for better writing
